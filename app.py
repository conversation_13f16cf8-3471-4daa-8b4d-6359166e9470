from typing import Optional
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):  # ✅ phải kế thừa từ BaseModel
    """Base schema cho User"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    """Schema cho tạo user mới"""
    password: str

user = UserCreate(
    username='byn',
    email='<EMAIL>',
    password='secret',
    full_name='By<PERSON>'
)

user_data = {
    "username":'byn',
    "email":'<EMAIL>',
    "password":'secret',
    "full_name":'By<PERSON>'
}
user1 = UserCreate(**user_data) # cách chuyển ở routes user_data : UserCreate
print(user1)
print(user)
print(dict(user))
