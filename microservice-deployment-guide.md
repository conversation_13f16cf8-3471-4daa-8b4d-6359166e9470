# Hướng Dẫn Deployment Microservice

## 1. Docker Compose Configuration

### 1.1 Main Docker Compose (docker-compose.yml)

```yaml
version: '3.8'

services:
  # Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: wound-detection-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - microservice-network
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: wound-detection-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - microservice-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # API Gateway
  api-gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8000:8000"
    environment:
      - USER_SERVICE_URL=http://user-service:8001
      - DETECTION_SERVICE_URL=http://wound-detection-service:8002
      - RECOMMENDATION_SERVICE_URL=http://recommendation-service:8003
      - NOTIFICATION_SERVICE_URL=http://notification-service:8004
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - microservice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Service
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: user-service
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=mssql+pyodbc://sa:YourStrong@Passw0rd@sqlserver:1433/WoundDetectionDB?driver=ODBC+Driver+17+for+SQL+Server
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-super-secret-jwt-key
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRATION_HOURS=24
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - microservice-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Wound Detection Service
  wound-detection-service:
    build:
      context: ./services/wound-detection-service
      dockerfile: Dockerfile
    container_name: wound-detection-service
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=mssql+pyodbc://sa:YourStrong@Passw0rd@sqlserver:1433/WoundDetectionDB?driver=ODBC+Driver+17+for+SQL+Server
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models
      - MAX_IMAGE_SIZE=10485760  # 10MB
      - ALLOWED_EXTENSIONS=jpg,jpeg,png,bmp
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - microservice-network
    volumes:
      - ./models:/app/models
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Recommendation Service
  recommendation-service:
    build:
      context: ./services/recommendation-service
      dockerfile: Dockerfile
    container_name: recommendation-service
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=mssql+pyodbc://sa:YourStrong@Passw0rd@sqlserver:1433/WoundDetectionDB?driver=ODBC+Driver+17+for+SQL+Server
      - REDIS_URL=redis://redis:6379
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - microservice-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: notification-service
    ports:
      - "8004:8004"
    environment:
      - DATABASE_URL=mssql+pyodbc://sa:YourStrong@Passw0rd@sqlserver:1433/WoundDetectionDB?driver=ODBC+Driver+17+for+SQL+Server
      - REDIS_URL=redis://redis:6379
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=your-app-password
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - microservice-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - microservice-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - microservice-network

volumes:
  sqlserver_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  microservice-network:
    driver: bridge
```

### 1.2 Development Docker Compose (docker-compose.dev.yml)

```yaml
version: '3.8'

services:
  # Override cho development
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile.dev  # Development Dockerfile
    volumes:
      - ./services/user-service:/app
      - ./shared:/app/shared
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]

  wound-detection-service:
    build:
      context: ./services/wound-detection-service
      dockerfile: Dockerfile.dev
    volumes:
      - ./services/wound-detection-service:/app
      - ./shared:/app/shared
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8002", "--reload"]

  recommendation-service:
    build:
      context: ./services/recommendation-service
      dockerfile: Dockerfile.dev
    volumes:
      - ./services/recommendation-service:/app
      - ./shared:/app/shared
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8003", "--reload"]

  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile.dev
    volumes:
      - ./services/notification-service:/app
      - ./shared:/app/shared
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8004", "--reload"]

  api-gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile.dev
    volumes:
      - ./gateway:/app
      - ./shared:/app/shared
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## 2. Environment Configuration

### 2.1 Environment Variables (.env)

```bash
# Database Configuration
DATABASE_URL=mssql+pyodbc://sa:YourStrong@Passw0rd@localhost:1433/WoundDetectionDB?driver=ODBC+Driver+17+for+SQL+Server
DB_HOST=localhost
DB_PORT=1433
DB_NAME=WoundDetectionDB
DB_USER=sa
DB_PASSWORD=YourStrong@Passw0rd

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Service URLs
USER_SERVICE_URL=http://localhost:8001
DETECTION_SERVICE_URL=http://localhost:8002
RECOMMENDATION_SERVICE_URL=http://localhost:8003
NOTIFICATION_SERVICE_URL=http://localhost:8004

# File Upload Configuration
MAX_IMAGE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,bmp
UPLOAD_FOLDER=./uploads

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Environment
ENVIRONMENT=development
DEBUG=true
```
