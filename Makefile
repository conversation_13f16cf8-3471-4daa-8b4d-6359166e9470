# Makefile for Microservice Project
# Tự động hóa các tác vụ development và deployment

.PHONY: help install build test clean dev prod stop logs migrate seed

# Default target
help: ## Hiển thị help
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Development Commands
install: ## Cài đặt dependencies cho tất cả services
	@echo "Installing dependencies for all services..."
	@for service in services/*/; do \
		if [ -f "$$service/requirements.txt" ]; then \
			echo "Installing dependencies for $$service"; \
			cd "$$service" && pip install -r requirements.txt && cd ../..; \
		fi \
	done
	@echo "Installing gateway dependencies..."
	@cd gateway && pip install -r requirements.txt && cd ..
	@echo "All dependencies installed!"

install-dev: ## Cài đặt development dependencies
	@echo "Installing development dependencies..."
	pip install -r requirements-dev.txt
	@echo "Development dependencies installed!"

setup: ## Setup dự án lần đầu
	@echo "Setting up project..."
	@cp .env.example .env
	@echo "Please edit .env file with your configuration"
	@make install
	@make install-dev
	@echo "Project setup completed!"

# Docker Commands
build: ## Build tất cả Docker images
	@echo "Building all Docker images..."
	docker-compose build
	@echo "All images built successfully!"

build-service: ## Build specific service (usage: make build-service SERVICE=user-service)
	@echo "Building $(SERVICE)..."
	docker-compose build $(SERVICE)
	@echo "$(SERVICE) built successfully!"

dev: ## Chạy development environment
	@echo "Starting development environment..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
	@echo "Development environment started!"
	@echo "Services available at:"
	@echo "  - API Gateway: http://localhost:8000"
	@echo "  - User Service: http://localhost:8001"
	@echo "  - Detection Service: http://localhost:8002"
	@echo "  - Recommendation Service: http://localhost:8003"
	@echo "  - Notification Service: http://localhost:8004"
	@echo "  - Grafana: http://localhost:3000 (admin/admin123)"
	@echo "  - Prometheus: http://localhost:9090"

prod: ## Chạy production environment
	@echo "Starting production environment..."
	docker-compose up -d
	@echo "Production environment started!"

stop: ## Dừng tất cả services
	@echo "Stopping all services..."
	docker-compose down
	@echo "All services stopped!"

restart: ## Restart tất cả services
	@echo "Restarting all services..."
	docker-compose restart
	@echo "All services restarted!"

restart-service: ## Restart specific service (usage: make restart-service SERVICE=user-service)
	@echo "Restarting $(SERVICE)..."
	docker-compose restart $(SERVICE)
	@echo "$(SERVICE) restarted!"

# Logs Commands
logs: ## Xem logs của tất cả services
	docker-compose logs -f

logs-service: ## Xem logs của service cụ thể (usage: make logs-service SERVICE=user-service)
	docker-compose logs -f $(SERVICE)

# Database Commands
migrate: ## Chạy database migrations
	@echo "Running database migrations..."
	@for service in services/*/; do \
		if [ -f "$$service/alembic.ini" ]; then \
			echo "Running migrations for $$service"; \
			cd "$$service" && alembic upgrade head && cd ../..; \
		fi \
	done
	@echo "All migrations completed!"

migrate-service: ## Chạy migration cho service cụ thể (usage: make migrate-service SERVICE=user-service)
	@echo "Running migration for $(SERVICE)..."
	cd services/$(SERVICE) && alembic upgrade head
	@echo "Migration for $(SERVICE) completed!"

create-migration: ## Tạo migration mới (usage: make create-migration SERVICE=user-service MESSAGE="add user table")
	@echo "Creating migration for $(SERVICE)..."
	cd services/$(SERVICE) && alembic revision --autogenerate -m "$(MESSAGE)"
	@echo "Migration created for $(SERVICE)!"

seed: ## Seed database với test data
	@echo "Seeding database..."
	python scripts/seed_database.py
	@echo "Database seeded!"

# Testing Commands
test: ## Chạy tất cả tests
	@echo "Running all tests..."
	pytest tests/ -v --cov=. --cov-report=html
	@echo "All tests completed!"

test-unit: ## Chạy unit tests
	@echo "Running unit tests..."
	pytest tests/unit/ -v
	@echo "Unit tests completed!"

test-integration: ## Chạy integration tests
	@echo "Running integration tests..."
	pytest tests/integration/ -v
	@echo "Integration tests completed!"

test-e2e: ## Chạy end-to-end tests
	@echo "Running e2e tests..."
	pytest tests/e2e/ -v
	@echo "E2E tests completed!"

test-service: ## Chạy tests cho service cụ thể (usage: make test-service SERVICE=user-service)
	@echo "Running tests for $(SERVICE)..."
	cd services/$(SERVICE) && pytest tests/ -v
	@echo "Tests for $(SERVICE) completed!"

# Code Quality Commands
lint: ## Chạy linting cho tất cả code
	@echo "Running linting..."
	flake8 services/ gateway/ shared/
	black --check services/ gateway/ shared/
	isort --check-only services/ gateway/ shared/
	@echo "Linting completed!"

format: ## Format code
	@echo "Formatting code..."
	black services/ gateway/ shared/
	isort services/ gateway/ shared/
	@echo "Code formatted!"

type-check: ## Chạy type checking
	@echo "Running type checking..."
	mypy services/ gateway/ shared/
	@echo "Type checking completed!"

security-check: ## Chạy security scanning
	@echo "Running security check..."
	bandit -r services/ gateway/ shared/
	safety check
	@echo "Security check completed!"

# Monitoring Commands
health-check: ## Kiểm tra health của tất cả services
	@echo "Checking service health..."
	@curl -f http://localhost:8000/health || echo "API Gateway: DOWN"
	@curl -f http://localhost:8001/health || echo "User Service: DOWN"
	@curl -f http://localhost:8002/health || echo "Detection Service: DOWN"
	@curl -f http://localhost:8003/health || echo "Recommendation Service: DOWN"
	@curl -f http://localhost:8004/health || echo "Notification Service: DOWN"
	@echo "Health check completed!"

metrics: ## Xem metrics
	@echo "Fetching metrics..."
	curl http://localhost:9090/api/v1/query?query=up
	@echo "Metrics fetched!"

# Cleanup Commands
clean: ## Dọn dẹp containers và images
	@echo "Cleaning up..."
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "Cleanup completed!"

clean-all: ## Dọn dẹp tất cả (bao gồm volumes)
	@echo "Cleaning up everything..."
	docker-compose down -v --remove-orphans
	docker system prune -a -f --volumes
	@echo "Everything cleaned up!"

# Backup Commands
backup-db: ## Backup database
	@echo "Backing up database..."
	docker exec wound-detection-db /opt/mssql-tools/bin/sqlcmd \
		-S localhost -U sa -P YourStrong@Passw0rd \
		-Q "BACKUP DATABASE WoundDetectionDB TO DISK = '/var/opt/mssql/backup/WoundDetectionDB.bak'"
	@echo "Database backup completed!"

restore-db: ## Restore database từ backup
	@echo "Restoring database..."
	docker exec wound-detection-db /opt/mssql-tools/bin/sqlcmd \
		-S localhost -U sa -P YourStrong@Passw0rd \
		-Q "RESTORE DATABASE WoundDetectionDB FROM DISK = '/var/opt/mssql/backup/WoundDetectionDB.bak' WITH REPLACE"
	@echo "Database restore completed!"

# Documentation Commands
docs: ## Generate API documentation
	@echo "Generating API documentation..."
	@for service in services/*/; do \
		if [ -f "$$service/main.py" ]; then \
			echo "Generating docs for $$service"; \
			cd "$$service" && python -c "from main import app; import json; print(json.dumps(app.openapi(), indent=2))" > docs/api.json && cd ../..; \
		fi \
	done
	@echo "API documentation generated!"

# Deployment Commands
deploy-staging: ## Deploy to staging environment
	@echo "Deploying to staging..."
	# Add your staging deployment commands here
	@echo "Deployed to staging!"

deploy-prod: ## Deploy to production environment
	@echo "Deploying to production..."
	# Add your production deployment commands here
	@echo "Deployed to production!"

# Utility Commands
shell: ## Mở shell trong container (usage: make shell SERVICE=user-service)
	docker-compose exec $(SERVICE) /bin/bash

db-shell: ## Mở database shell
	docker-compose exec sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd

redis-shell: ## Mở Redis shell
	docker-compose exec redis redis-cli

# Development Helpers
watch-logs: ## Watch logs với filtering (usage: make watch-logs FILTER=ERROR)
	docker-compose logs -f | grep "$(FILTER)"

ps: ## Hiển thị status của tất cả containers
	docker-compose ps

top: ## Hiển thị resource usage
	docker stats

# Quick commands
up: dev ## Alias cho dev
down: stop ## Alias cho stop
