# Best Practices cho Microservice Development

## 1. Design Principles

### 1.1 Single Responsibility Principle
- Mỗi service chỉ chịu trách nhiệm cho một domain cụ thể
- Tránh tạo service quá lớn (monolithic trong microservice)
- Service nên c<PERSON> thể được maintain bởi một team nhỏ (2-8 người)

### 1.2 Database per Service
- Mỗi service có database riêng
- Không share database giữa các services
- Sử dụng API calls thay vì direct database access

### 1.3 Stateless Services
- Services không lưu trữ state giữa các requests
- State được lưu trong database hoặc cache
- Dễ dàng scale horizontal

## 2. Communication Patterns

### 2.1 Synchronous Communication (HTTP/REST)
```python
# Example: Service-to-service HTTP call
import httpx
from typing import Optional

class UserServiceClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get_user(self, user_id: int) -> Optional[dict]:
        """Lấy thông tin user từ User Service"""
        try:
            response = await self.client.get(f"{self.base_url}/users/{user_id}")
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            print(f"Error calling user service: {e}")
            return None
    
    async def close(self):
        await self.client.aclose()
```

### 2.2 Asynchronous Communication (Message Queue)
```python
# Example: RabbitMQ với Celery
from celery import Celery

# Celery configuration
celery_app = Celery(
    'wound_detection',
    broker='redis://localhost:6379/0',
    backend='redis://localhost:6379/0'
)

@celery_app.task
def process_wound_image(image_path: str, user_id: int):
    """
    Background task để xử lý ảnh vết thương
    Chạy async để không block main thread
    """
    # AI processing logic here
    result = analyze_wound_image(image_path)
    
    # Send notification
    send_notification.delay(user_id, result)
    
    return result

@celery_app.task
def send_notification(user_id: int, detection_result: dict):
    """Background task để gửi thông báo"""
    # Notification logic here
    pass
```

## 3. Error Handling & Resilience

### 3.1 Circuit Breaker Pattern
```python
import asyncio
from enum import Enum
from datetime import datetime, timedelta

class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open" # Testing if service recovered

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    async def call(self, func, *args, **kwargs):
        """Execute function với circuit breaker protection"""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        return (
            self.last_failure_time and
            datetime.now() - self.last_failure_time > timedelta(seconds=self.timeout)
        )
    
    def _on_success(self):
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
```

### 3.2 Retry Pattern với Exponential Backoff
```python
import asyncio
import random
from typing import Callable, Any

async def retry_with_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True
) -> Any:
    """
    Retry function với exponential backoff
    """
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries:
                raise e
            
            # Calculate delay
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            
            # Add jitter để tránh thundering herd
            if jitter:
                delay *= (0.5 + random.random() * 0.5)
            
            print(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay:.2f}s...")
            await asyncio.sleep(delay)

# Usage example
async def unreliable_api_call():
    # Simulate API call that might fail
    if random.random() < 0.7:  # 70% failure rate
        raise Exception("API call failed")
    return {"status": "success"}

# Sử dụng retry
result = await retry_with_backoff(unreliable_api_call, max_retries=3)
```

## 4. Testing Strategies

### 4.1 Unit Testing
```python
# tests/test_services/test_user_service.py
import pytest
from unittest.mock import Mock, AsyncMock
from app.services.user_service import UserService
from app.schemas.user_schema import UserCreate
from shared.utils.exceptions import ConflictError

class TestUserService:
    @pytest.fixture
    def mock_user_repository(self):
        return Mock()
    
    @pytest.fixture
    def user_service(self, mock_user_repository):
        return UserService(mock_user_repository)
    
    def test_create_user_success(self, user_service, mock_user_repository):
        # Arrange
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        mock_user_repository.get_by_email.return_value = None
        mock_user_repository.get_by_username.return_value = None
        mock_user_repository.create.return_value = Mock(id=1, username="testuser")
        
        # Act
        result = user_service.create_user(user_data)
        
        # Assert
        assert result is not None
        mock_user_repository.create.assert_called_once()
    
    def test_create_user_email_exists(self, user_service, mock_user_repository):
        # Arrange
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        mock_user_repository.get_by_email.return_value = Mock(id=1)
        
        # Act & Assert
        with pytest.raises(ConflictError, match="Email already registered"):
            user_service.create_user(user_data)
```

### 4.2 Integration Testing
```python
# tests/integration/test_user_workflow.py
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from main import app
from shared.database.base import Base
from shared.database.connection import get_db

# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

class TestUserWorkflow:
    def test_complete_user_workflow(self, client):
        # 1. Create user
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Test User"
        }
        response = client.post("/api/v1/users/", json=user_data)
        assert response.status_code == 201
        created_user = response.json()["data"]
        user_id = created_user["id"]
        
        # 2. Login
        login_data = {
            "username": "testuser",
            "password": "password123"
        }
        response = client.post("/api/v1/users/login", json=login_data)
        assert response.status_code == 200
        
        # 3. Get user
        response = client.get(f"/api/v1/users/{user_id}")
        assert response.status_code == 200
        user = response.json()["data"]
        assert user["username"] == "testuser"
        
        # 4. Update user
        update_data = {"full_name": "Updated Name"}
        response = client.put(f"/api/v1/users/{user_id}", json=update_data)
        assert response.status_code == 200
        updated_user = response.json()["data"]
        assert updated_user["full_name"] == "Updated Name"
        
        # 5. Delete user
        response = client.delete(f"/api/v1/users/{user_id}")
        assert response.status_code == 200
```

### 4.3 Contract Testing
```python
# tests/contract/test_user_service_contract.py
import pytest
from pact import Consumer, Provider, Like, Term
from fastapi.testclient import TestClient
from main import app

# Pact setup
pact = Consumer('wound-detection-service').has_pact_with(Provider('user-service'))

class TestUserServiceContract:
    def test_get_user_contract(self):
        # Define expected interaction
        expected = {
            'id': Like(1),
            'username': Like('testuser'),
            'email': Like('<EMAIL>'),
            'full_name': Like('Test User'),
            'is_active': Like(True),
            'created_at': Term(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', '2023-01-01T00:00:00')
        }
        
        (pact
         .given('user exists')
         .upon_receiving('a request for user')
         .with_request('GET', '/api/v1/users/1')
         .will_respond_with(200, body=expected))
        
        with pact:
            client = TestClient(app)
            response = client.get('/api/v1/users/1')
            assert response.status_code == 200
```

## 5. Monitoring & Observability

### 5.1 Structured Logging
```python
# shared/utils/logger.py
import logging
import json
from datetime import datetime
from typing import Any, Dict

class JSONFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'service': getattr(record, 'service', 'unknown'),
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'duration'):
            log_entry['duration'] = record.duration
            
        return json.dumps(log_entry)

def setup_logger(service_name: str) -> logging.Logger:
    logger = logging.getLogger(service_name)
    logger.setLevel(logging.INFO)
    
    handler = logging.StreamHandler()
    handler.setFormatter(JSONFormatter())
    logger.addHandler(handler)
    
    return logger

# Usage
logger = setup_logger('user-service')
logger.info('User created', extra={'user_id': 123, 'request_id': 'req-456'})
```

### 5.2 Metrics Collection
```python
# shared/middleware/metrics_middleware.py
from prometheus_client import Counter, Histogram, generate_latest
from fastapi import Request, Response
import time

# Metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code', 'service']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint', 'service']
)

class MetricsMiddleware:
    def __init__(self, app, service_name: str):
        self.app = app
        self.service_name = service_name
    
    async def __call__(self, request: Request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        duration = time.time() - start_time
        
        # Record metrics
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status_code=response.status_code,
            service=self.service_name
        ).inc()
        
        REQUEST_DURATION.labels(
            method=request.method,
            endpoint=request.url.path,
            service=self.service_name
        ).observe(duration)
        
        return response
```
