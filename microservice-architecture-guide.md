# Cấu Trú<PERSON>n Thiện Dự Án Microservice Python

## 1. Tổng Quan Kiến Trúc Microservice

Microservice là một kiến trúc phần mềm chia ứng dụng thành nhiều service nhỏ, độc lập, có thể triển khai riêng biệt. Mỗi service chịu trách nhiệm cho một chức năng cụ thể và giao tiếp qua API.

### Ưu điểm:
- **Scalability**: Có thể scale từng service riêng biệt
- **Technology Diversity**: Mỗi service có thể dùng công nghệ khác nhau
- **Fault Isolation**: Lỗi ở một service không ảnh hưởng toàn bộ hệ thống
- **Team Independence**: Các team có thể phát triển độc lập

### Nhược điểm:
- **Complexity**: <PERSON>ức tạp hơn monolithic
- **Network Latency**: <PERSON>iao <PERSON> qua network
- **Data Consistency**: <PERSON><PERSON><PERSON> đ<PERSON> bả<PERSON> ACID transactions

## 2. Cấu Trúc Dự Án Microservice Hoàn Thiện

```
microservice-project/
├── services/                          # Thư mục chứa tất cả microservices
│   ├── user-service/                  # Service quản lý người dùng
│   │   ├── app/
│   │   │   ├── __init__.py
│   │   │   ├── models/                # Database models
│   │   │   │   ├── __init__.py
│   │   │   │   └── user.py
│   │   │   ├── controllers/           # API controllers
│   │   │   │   ├── __init__.py
│   │   │   │   └── user_controller.py
│   │   │   ├── services/              # Business logic
│   │   │   │   ├── __init__.py
│   │   │   │   └── user_service.py
│   │   │   ├── repositories/          # Data access layer
│   │   │   │   ├── __init__.py
│   │   │   │   └── user_repository.py
│   │   │   ├── schemas/               # Pydantic schemas
│   │   │   │   ├── __init__.py
│   │   │   │   └── user_schema.py
│   │   │   ├── utils/                 # Utilities
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth.py
│   │   │   │   └── validators.py
│   │   │   └── config.py              # Configuration
│   │   ├── tests/                     # Unit tests
│   │   │   ├── __init__.py
│   │   │   ├── test_controllers/
│   │   │   ├── test_services/
│   │   │   └── test_repositories/
│   │   ├── migrations/                # Database migrations
│   │   ├── requirements.txt           # Dependencies
│   │   ├── Dockerfile                 # Container configuration
│   │   ├── docker-compose.yml         # Local development
│   │   ├── .env.example               # Environment variables template
│   │   └── main.py                    # Application entry point
│   │
│   ├── wound-detection-service/       # Service AI phân tích vết thương
│   │   ├── app/
│   │   │   ├── models/
│   │   │   │   ├── wound.py
│   │   │   │   └── detection_result.py
│   │   │   ├── controllers/
│   │   │   │   └── detection_controller.py
│   │   │   ├── services/
│   │   │   │   ├── ai_service.py      # AI model integration
│   │   │   │   └── image_service.py   # Image processing
│   │   │   ├── ml_models/             # AI models
│   │   │   │   ├── wound_classifier.py
│   │   │   │   └── severity_detector.py
│   │   │   └── utils/
│   │   │       ├── image_processor.py
│   │   │       └── model_loader.py
│   │   ├── tests/
│   │   ├── requirements.txt
│   │   ├── Dockerfile
│   │   └── main.py
│   │
│   ├── recommendation-service/        # Service đưa ra khuyến nghị
│   │   ├── app/
│   │   │   ├── models/
│   │   │   │   └── recommendation.py
│   │   │   ├── controllers/
│   │   │   │   └── recommendation_controller.py
│   │   │   ├── services/
│   │   │   │   └── recommendation_service.py
│   │   │   └── data/
│   │   │       └── first_aid_rules.json
│   │   ├── tests/
│   │   ├── requirements.txt
│   │   ├── Dockerfile
│   │   └── main.py
│   │
│   └── notification-service/          # Service thông báo
│       ├── app/
│       │   ├── models/
│       │   ├── controllers/
│       │   ├── services/
│       │   └── utils/
│       ├── tests/
│       ├── requirements.txt
│       ├── Dockerfile
│       └── main.py
│
├── shared/                            # Code dùng chung
│   ├── __init__.py
│   ├── database/                      # Database utilities
│   │   ├── __init__.py
│   │   ├── base.py                    # Base model class
│   │   └── connection.py              # Database connection
│   ├── middleware/                    # Middleware dùng chung
│   │   ├── __init__.py
│   │   ├── auth_middleware.py
│   │   ├── cors_middleware.py
│   │   └── logging_middleware.py
│   ├── utils/                         # Utilities dùng chung
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── exceptions.py
│   │   └── response_formatter.py
│   └── schemas/                       # Schemas dùng chung
│       ├── __init__.py
│       └── base_schema.py
│
├── infrastructure/                    # Infrastructure as Code
│   ├── docker/                        # Docker configurations
│   │   ├── docker-compose.yml         # All services
│   │   ├── docker-compose.dev.yml     # Development
│   │   └── docker-compose.prod.yml    # Production
│   ├── kubernetes/                    # K8s manifests
│   │   ├── deployments/
│   │   ├── services/
│   │   ├── configmaps/
│   │   └── secrets/
│   ├── terraform/                     # Infrastructure provisioning
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── monitoring/                    # Monitoring configs
│       ├── prometheus/
│       ├── grafana/
│       └── elk/
│
├── gateway/                           # API Gateway
│   ├── app/
│   │   ├── __init__.py
│   │   ├── routes/                    # Route definitions
│   │   │   ├── __init__.py
│   │   │   ├── user_routes.py
│   │   │   ├── detection_routes.py
│   │   │   └── recommendation_routes.py
│   │   ├── middleware/                # Gateway middleware
│   │   │   ├── __init__.py
│   │   │   ├── rate_limiter.py
│   │   │   ├── auth_guard.py
│   │   │   └── request_logger.py
│   │   ├── services/                  # Service discovery
│   │   │   ├── __init__.py
│   │   │   └── service_registry.py
│   │   └── config.py
│   ├── tests/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── main.py
│
├── docs/                              # Documentation
│   ├── api/                           # API documentation
│   │   ├── user-service.md
│   │   ├── wound-detection-service.md
│   │   └── recommendation-service.md
│   ├── architecture/                  # Architecture docs
│   │   ├── system-design.md
│   │   ├── database-design.md
│   │   └── deployment-guide.md
│   └── development/                   # Development guides
│       ├── setup-guide.md
│       ├── coding-standards.md
│       └── testing-guide.md
│
├── scripts/                           # Automation scripts
│   ├── setup.sh                       # Project setup
│   ├── build.sh                       # Build all services
│   ├── deploy.sh                      # Deployment script
│   ├── test.sh                        # Run all tests
│   └── migrate.sh                     # Database migrations
│
├── tests/                             # Integration tests
│   ├── __init__.py
│   ├── integration/                   # Cross-service tests
│   │   ├── test_user_workflow.py
│   │   └── test_detection_workflow.py
│   ├── e2e/                          # End-to-end tests
│   │   └── test_complete_flow.py
│   └── performance/                   # Performance tests
│       └── test_load.py
│
├── .github/                           # GitHub workflows
│   └── workflows/
│       ├── ci.yml                     # Continuous Integration
│       ├── cd.yml                     # Continuous Deployment
│       └── security.yml               # Security scanning
│
├── .gitignore                         # Git ignore rules
├── README.md                          # Project overview
├── docker-compose.yml                 # Main compose file
├── requirements-dev.txt               # Development dependencies
└── Makefile                          # Build automation
```

## 3. Chi Tiết Từng Thành Phần

### 3.1 Service Structure (Cấu trúc mỗi Service)

Mỗi microservice tuân theo pattern **Clean Architecture**:

#### Models (app/models/)
- Định nghĩa database models sử dụng SQLAlchemy
- Chứa business entities và domain logic
- Ví dụ: User, Wound, DetectionResult

#### Controllers (app/controllers/)
- Xử lý HTTP requests/responses
- Validation input data
- Gọi business logic từ services
- Return formatted responses

#### Services (app/services/)
- Chứa business logic chính
- Orchestrate các operations phức tạp
- Gọi repositories để truy cập data
- Implement business rules

#### Repositories (app/repositories/)
- Data access layer
- Trừu tượng hóa database operations
- Implement CRUD operations
- Query optimization

#### Schemas (app/schemas/)
- Pydantic models cho validation
- Request/Response serialization
- Data transformation

### 3.2 Shared Components (Thành phần dùng chung)

#### Database (shared/database/)
- Base model class với common fields
- Database connection management
- Migration utilities

#### Middleware (shared/middleware/)
- Authentication middleware
- CORS handling
- Request logging
- Error handling

#### Utils (shared/utils/)
- Common utilities
- Logger configuration
- Custom exceptions
- Response formatters

## 4. Implementation Chi Tiết

### 4.1 Base Model Class (shared/database/base.py)

```python
from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class BaseModel(Base):
    """Base model class với các fields chung"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def to_dict(self):
        """Convert model instance to dictionary"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
```

### 4.2 Service Template (services/user-service/app/models/user.py)

```python
from sqlalchemy import Column, String, Boolean, DateTime
from shared.database.base import BaseModel

class User(BaseModel):
    """User model cho authentication và profile"""
    __tablename__ = "users"

    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"
```

### 4.3 Repository Pattern (services/user-service/app/repositories/user_repository.py)

```python
from typing import Optional, List
from sqlalchemy.orm import Session
from app.models.user import User
from shared.utils.exceptions import NotFoundError

class UserRepository:
    """Repository pattern cho User operations"""

    def __init__(self, db: Session):
        self.db = db

    def create(self, user_data: dict) -> User:
        """Tạo user mới"""
        user = User(**user_data)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_by_id(self, user_id: int) -> Optional[User]:
        """Lấy user theo ID"""
        return self.db.query(User).filter(User.id == user_id).first()

    def get_by_email(self, email: str) -> Optional[User]:
        """Lấy user theo email"""
        return self.db.query(User).filter(User.email == email).first()

    def get_by_username(self, username: str) -> Optional[User]:
        """Lấy user theo username"""
        return self.db.query(User).filter(User.username == username).first()

    def update(self, user_id: int, update_data: dict) -> User:
        """Cập nhật user"""
        user = self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with id {user_id} not found")

        for key, value in update_data.items():
            setattr(user, key, value)

        self.db.commit()
        self.db.refresh(user)
        return user

    def delete(self, user_id: int) -> bool:
        """Xóa user"""
        user = self.get_by_id(user_id)
        if not user:
            return False

        self.db.delete(user)
        self.db.commit()
        return True

    def list_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Lấy danh sách users với pagination"""
        return self.db.query(User).offset(skip).limit(limit).all()
```

### 4.4 Service Layer (services/user-service/app/services/user_service.py)

```python
from typing import Optional, List
from passlib.context import CryptContext
from app.repositories.user_repository import UserRepository
from app.schemas.user_schema import UserCreate, UserUpdate, UserResponse
from shared.utils.exceptions import ValidationError, ConflictError

class UserService:
    """Business logic cho User operations"""

    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def create_user(self, user_data: UserCreate) -> UserResponse:
        """Tạo user mới với validation"""
        # Kiểm tra email đã tồn tại
        if self.user_repository.get_by_email(user_data.email):
            raise ConflictError("Email already registered")

        # Kiểm tra username đã tồn tại
        if self.user_repository.get_by_username(user_data.username):
            raise ConflictError("Username already taken")

        # Hash password
        hashed_password = self.pwd_context.hash(user_data.password)

        # Tạo user data
        user_dict = user_data.dict(exclude={'password'})
        user_dict['hashed_password'] = hashed_password

        # Tạo user
        user = self.user_repository.create(user_dict)
        return UserResponse.from_orm(user)

    def authenticate_user(self, username: str, password: str) -> Optional[UserResponse]:
        """Xác thực user"""
        user = self.user_repository.get_by_username(username)
        if not user:
            return None

        if not self.pwd_context.verify(password, user.hashed_password):
            return None

        return UserResponse.from_orm(user)

    def get_user(self, user_id: int) -> Optional[UserResponse]:
        """Lấy thông tin user"""
        user = self.user_repository.get_by_id(user_id)
        if not user:
            return None
        return UserResponse.from_orm(user)

    def update_user(self, user_id: int, update_data: UserUpdate) -> UserResponse:
        """Cập nhật thông tin user"""
        update_dict = update_data.dict(exclude_unset=True)

        # Hash password nếu có update
        if 'password' in update_dict:
            update_dict['hashed_password'] = self.pwd_context.hash(update_dict.pop('password'))

        user = self.user_repository.update(user_id, update_dict)
        return UserResponse.from_orm(user)

    def delete_user(self, user_id: int) -> bool:
        """Xóa user"""
        return self.user_repository.delete(user_id)

    def list_users(self, skip: int = 0, limit: int = 100) -> List[UserResponse]:
        """Lấy danh sách users"""
        users = self.user_repository.list_users(skip, limit)
        return [UserResponse.from_orm(user) for user in users]

### 4.5 Pydantic Schemas (services/user-service/app/schemas/user_schema.py)

```python
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, validator

class UserBase(BaseModel):
    """Base schema cho User"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    """Schema cho tạo user mới"""
    password: str

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        return v

class UserUpdate(BaseModel):
    """Schema cho cập nhật user"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    """Schema cho response user data"""
    id: int
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        orm_mode = True

class UserLogin(BaseModel):
    """Schema cho login"""
    username: str
    password: str
```

### 4.6 Controller Layer (services/user-service/app/controllers/user_controller.py)

```python
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.services.user_service import UserService
from app.repositories.user_repository import UserRepository
from app.schemas.user_schema import UserCreate, UserUpdate, UserResponse, UserLogin
from shared.database.connection import get_db
from shared.utils.exceptions import NotFoundError, ConflictError, ValidationError
from shared.utils.response_formatter import success_response, error_response

router = APIRouter(prefix="/users", tags=["users"])

def get_user_service(db: Session = Depends(get_db)) -> UserService:
    """Dependency injection cho UserService"""
    user_repository = UserRepository(db)
    return UserService(user_repository)

@router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    user_service: UserService = Depends(get_user_service)
):
    """
    Tạo user mới

    - **username**: Tên đăng nhập (unique)
    - **email**: Email (unique)
    - **password**: Mật khẩu (tối thiểu 8 ký tự)
    - **full_name**: Họ tên đầy đủ (optional)
    """
    try:
        user = user_service.create_user(user_data)
        return success_response(
            data=user.dict(),
            message="User created successfully"
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )

@router.post("/login", response_model=dict)
async def login(
    login_data: UserLogin,
    user_service: UserService = Depends(get_user_service)
):
    """
    Đăng nhập user

    - **username**: Tên đăng nhập
    - **password**: Mật khẩu
    """
    user = user_service.authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )

    return success_response(
        data=user.dict(),
        message="Login successful"
    )

@router.get("/{user_id}", response_model=dict)
async def get_user(
    user_id: int,
    user_service: UserService = Depends(get_user_service)
):
    """Lấy thông tin user theo ID"""
    user = user_service.get_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return success_response(
        data=user.dict(),
        message="User retrieved successfully"
    )

@router.put("/{user_id}", response_model=dict)
async def update_user(
    user_id: int,
    update_data: UserUpdate,
    user_service: UserService = Depends(get_user_service)
):
    """Cập nhật thông tin user"""
    try:
        user = user_service.update_user(user_id, update_data)
        return success_response(
            data=user.dict(),
            message="User updated successfully"
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )

@router.delete("/{user_id}", response_model=dict)
async def delete_user(
    user_id: int,
    user_service: UserService = Depends(get_user_service)
):
    """Xóa user"""
    success = user_service.delete_user(user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return success_response(
        message="User deleted successfully"
    )

@router.get("/", response_model=dict)
async def list_users(
    skip: int = 0,
    limit: int = 100,
    user_service: UserService = Depends(get_user_service)
):
    """Lấy danh sách users với pagination"""
    users = user_service.list_users(skip, limit)
    return success_response(
        data=[user.dict() for user in users],
        message="Users retrieved successfully"
    )

### 4.7 Database Connection (shared/database/connection.py)

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import os
from typing import Generator

# Database URL từ environment variables
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server"
)

# Tạo engine với connection pooling
engine = create_engine(
    DATABASE_URL,
    poolclass=StaticPool,
    pool_pre_ping=True,  # Kiểm tra connection trước khi sử dụng
    pool_recycle=3600,   # Recycle connection sau 1 giờ
    echo=False           # Set True để log SQL queries
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db() -> Generator[Session, None, None]:
    """
    Dependency để inject database session
    Tự động đóng session sau khi sử dụng
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Tạo tất cả tables từ models"""
    from shared.database.base import Base
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Xóa tất cả tables (chỉ dùng trong development)"""
    from shared.database.base import Base
    Base.metadata.drop_all(bind=engine)
```

### 4.8 Main Application (services/user-service/main.py)

```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from contextlib import asynccontextmanager

from app.controllers.user_controller import router as user_router
from shared.database.connection import create_tables
from shared.middleware.logging_middleware import LoggingMiddleware
from shared.utils.exceptions import ValidationError, NotFoundError, ConflictError
from shared.utils.logger import setup_logger

# Setup logger
logger = setup_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifecycle events cho application
    Chạy khi start và shutdown
    """
    # Startup
    logger.info("Starting User Service...")
    create_tables()  # Tạo tables nếu chưa có
    logger.info("Database tables created/verified")

    yield

    # Shutdown
    logger.info("Shutting down User Service...")

# Tạo FastAPI app
app = FastAPI(
    title="User Service",
    description="Microservice quản lý người dùng cho hệ thống phân tích vết thương",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Trong production nên specify domains cụ thể
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Custom middleware
app.add_middleware(LoggingMiddleware)

# Exception handlers
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "error": str(exc)
        }
    )

@app.exception_handler(NotFoundError)
async def not_found_exception_handler(request: Request, exc: NotFoundError):
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "Resource not found",
            "error": str(exc)
        }
    )

@app.exception_handler(ConflictError)
async def conflict_exception_handler(request: Request, exc: ConflictError):
    return JSONResponse(
        status_code=409,
        content={
            "success": False,
            "message": "Conflict error",
            "error": str(exc)
        }
    )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint cho monitoring"""
    return {
        "status": "healthy",
        "service": "user-service",
        "version": "1.0.0"
    }

# Include routers
app.include_router(user_router, prefix="/api/v1")

if __name__ == "__main__":
    # Chạy server
    port = int(os.getenv("PORT", 8001))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,  # Auto-reload trong development
        log_level="info"
    )
```

### 4.9 Docker Configuration (services/user-service/Dockerfile)

```dockerfile
# Multi-stage build để optimize image size
FROM python:3.11-slim as builder

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    unixodbc-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC Driver for SQL Server
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17

# Copy requirements và install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    unixodbc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC Driver
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17

# Copy installed packages từ builder stage
COPY --from=builder /root/.local /root/.local

# Copy application code
COPY . .

# Copy shared code
COPY ../../shared ./shared

# Set environment variables
ENV PYTHONPATH=/app
ENV PATH=/root/.local/bin:$PATH

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run application
CMD ["python", "main.py"]
```
```
```
